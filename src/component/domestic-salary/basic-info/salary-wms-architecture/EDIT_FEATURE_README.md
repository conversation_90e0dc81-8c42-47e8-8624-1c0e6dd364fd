# WMS架构与子仓映射 - 编辑功能实现说明

## 功能概述
在现有的 WMS架构与子仓映射页面中添加了编辑功能，用户可以编辑已有的映射记录。

## 实现的功能

### 1. 表格操作列
- 在列表表格中新增了"操作"列
- 每行记录都有一个"编辑"按钮
- 点击编辑按钮会打开编辑弹窗

### 2. 编辑弹窗
- 复用了现有的新增弹窗组件
- 弹窗标题会根据模式动态显示：
  - 新增模式：显示"新增明细"
  - 编辑模式：显示"编辑明细"

### 3. 数据预填充
- 编辑时会调用 `getDetailAPI` 接口获取当前记录的详细信息
- 自动填充表单字段：
  - 现有片区名称 (currentRegionName)
  - 现有园区名称 (currentParkName)
  - 现有子仓名称 (currentSubWarehouseName)
  - 规范子仓名称 (subWarehouseName)

### 4. 保存逻辑
- 新增和编辑共用同一个保存方法 `handleregister`
- 根据 `isEditMode` 状态判断调用新增API还是编辑API
- 编辑模式下会在参数中包含记录ID

## 修改的文件

### 1. jsx/list.jsx
- 导入 Button 组件
- 在 columns 配置中添加操作列
- 操作列包含编辑按钮，点击调用 `store.handleEdit(record)`

### 2. jsx/handle.jsx
- 添加 `isEditMode` 属性
- 弹窗标题根据编辑模式动态显示
- 更新 PropTypes

### 3. reducers.js
- 导入 `registerEditAPI` 和 `getDetailAPI`
- 添加编辑相关状态：
  - `isEditMode`: 是否为编辑模式
  - `editingId`: 正在编辑的记录ID
- 修改 `handleregister` 方法支持编辑
- 新增 `handleEdit` 方法处理编辑操作
- 修改 `handleCloseRegisterModal` 重置编辑状态

## API 接口

### 使用的接口
1. `getDetailAPI` - 获取记录详情，用于编辑时预填充数据
2. `registerEditAPI` - 提交编辑后的数据
3. `registerAPI` - 新增记录（原有功能）

### 接口参数
- `getDetailAPI`: `{ id: recordId }`
- `registerEditAPI`: 包含所有表单字段 + `id` 字段

## 用户体验
1. 编辑成功后显示"编辑成功"提示
2. 编辑失败时显示错误信息
3. 获取详情失败时显示"获取详情失败"提示
4. 编辑成功后自动关闭弹窗并刷新列表数据

## 兼容性
- 完全兼容现有的新增功能
- 新增和编辑共用同一个弹窗组件
- 不影响现有的搜索、分页等功能
