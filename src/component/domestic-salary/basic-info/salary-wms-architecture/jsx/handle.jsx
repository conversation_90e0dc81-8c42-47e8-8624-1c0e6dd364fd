import React from 'react';
import PropTypes from 'prop-types';
import {
  Button, Modal, Form, Input, Select, Rule,
} from 'shineout';
import { t } from '@shein-bbl/react';
import styles from '../style.less';
import store from '../reducers';

const rules = Rule();
class Handle extends React.Component {
  render() {
    const {
      loading,
      addModalVisible,
      addModalData,
      subWarehouseList,
      isEditMode,
    } = this.props;
    return (
      <section className={styles.handleHandle}>
        <Button
          type="primary"
          disabled={!loading}
          onClick={() => {
            store.changeData({
              addModalVisible: true,
            });
          }}
        >
          {t('新增')}
        </Button>

        {/* 登记 */}
        <Modal
          maskCloseAble={null}
          width={420}
          visible={addModalVisible}
          title={t(isEditMode ? '编辑明细' : '新增明细')}
          onClose={() => {
            store.handleCloseRegisterModal();
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                store.handleCloseRegisterModal();
              }}
            >
              {t('取消')}
            </Button>,
            <Modal.Submit disabled={!loading}>{t('确认')}</Modal.Submit>,
          ]}
        >
          <Form
            labelWidth={100}
            labelAlign="right"
            style={{ width: 420 }}
            onSubmit={() => store.handleregister()}
            onChange={(value) => {
              store.changeaddModalData(value);
            }}
            value={addModalData}
            inline
            formRef={(f) => store.changeData({ formRef: f })}
          >
            <Form.Item required label={t('规范子仓名称')}>
              <Select
                autoAdapt
                name="subWarehouseName"
                data={subWarehouseList}
                keygen="id"
                format="nameZh"
                renderItem="nameZh"
                width={220}
                rules={[rules.required(t('请选择'))]}
                placeholder={t('请选择')}
                onFilter={(text) => (d) => d.nameZh.toLowerCase().indexOf(text.toLowerCase()) >= 0}
              />
            </Form.Item>
            <Form.Item required label={t('现有片区名称')}>
              <Input
                name="currentRegionName"
                maxLength={50}
                placeholder={t('请输入')}
                style={{ width: 220 }}
                rules={[rules.required(t('请输入'))]}
              />
            </Form.Item>
            <Form.Item required label={t('现有园区名称')}>
              <Input
                name="currentParkName"
                maxLength={50}
                placeholder={t('请输入')}
                style={{ width: 220 }}
                rules={[rules.required(t('请输入'))]}
              />
            </Form.Item>
            <Form.Item required label={t('现有子仓名称')}>
              <Input
                name="currentSubWarehouseName"
                maxLength={50}
                placeholder={t('请输入')}
                style={{ width: 220 }}
                rules={[rules.required(t('请输入'))]}
              />
            </Form.Item>
          </Form>
        </Modal>
      </section>
    );
  }
}

Handle.propTypes = {
  loading: PropTypes.number,
  addModalVisible: PropTypes.bool,
  addModalData: PropTypes.shape(),
  subWarehouseList: PropTypes.arrayOf(PropTypes.shape()),
  isEditMode: PropTypes.bool,
};
export default Handle;
