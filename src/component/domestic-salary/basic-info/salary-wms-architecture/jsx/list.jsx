import React from 'react';
import PropTypes from 'prop-types';
import { t } from '@shein-bbl/react';
import { Table, Button } from 'shineout';
import SearchAreaTable from '@public-component/search-queries/tableSorter-container';
import { handleTablePros } from '@src/lib/deal-func';
import globalStyles from '@src/component/style.less';
import store from '../reducers';

class List extends React.Component {
  render() {
    const {
      loading,
      list,
      pageInfo,
    } = this.props;

    const columns = [
      {
        title: t('现有片区名称'),
        render: 'currentRegionName',
        width: 130,
      },
      {
        title: t('规范片区名称'),
        render: 'regionName',
        width: 150,
      },
      {
        title: t('现有园区名称'),
        render: 'currentParkName',
        width: 180,
      },
      {
        title: t('规范园区名称'),
        render: 'parkName',
        width: 180,
      },
      {
        title: t('现有子仓名称'),
        render: 'currentSubWarehouseName',
        width: 180,
      },
      {
        title: t('规范子仓名称'),
        render: 'subWarehouseName',
        width: 150,
      },
      {
        title: t('登记时间'),
        render: 'createTime',
        width: 180,
      },
      {
        title: t('操作人'),
        render: 'creator',
        width: 180,
      },
      {
        title: t('操作'),
        fixed: 'right',
        width: 80,
        align: 'center',
        render: (record) => (
          <Button
            size="small"
            text
            type="primary"
            className={globalStyles.tableTextButton}
            onClick={() => {
              store.handleEdit(record);
            }}
          >
            {t('编辑')}
          </Button>
        ),
      },
    ];

    return (
      <section className={globalStyles.tableSection}>
        <SearchAreaTable>
          <Table
            {...handleTablePros(columns)}
            loading={!loading}
            data={list}
            keygen="id"
            pagination={{
              align: 'right',
              current: pageInfo.pageNum,
              pageSize: pageInfo.pageSize,
              className: globalStyles.pagination,
              layout: [({ total }) => `${t('共')} ${String(total)} ${t('条')}`, 'links', 'list', () => t('跳至'), 'jumper', () => t('页')],
              onChange: (page, size) => {
                store.handlePaginationChange({
                  pageNum: page,
                  pageSize: size,
                });
              },
              pageSizeList: pageInfo.pageSizeList,
              total: pageInfo.count,
            }}
          />
        </SearchAreaTable>
      </section>
    );
  }
}

List.propTypes = {
  loading: PropTypes.number,
  list: PropTypes.arrayOf(PropTypes.shape()),
  pageInfo: PropTypes.shape(),
};

export default List;
