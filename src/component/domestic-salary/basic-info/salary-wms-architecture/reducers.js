import { markStatus } from 'rrc-loader-helper';
import { t } from '@shein-bbl/react';
import { Modal, Message } from 'shineout';
import { getSize } from '@src/middlewares/pagesize';
import { clearEmpty, paramTrim } from '@src/lib/deal-func';
import {
  getListAPI, registerAPI, registerEditAPI, getDetailAPI, queryAllSubWarehouseDataAPI,
} from './server';

const defaultAddModalData = {
  currentParkName: '', // 现有园区名称
  currentRegionName: '', // 现有片区名称
  currentSubWarehouseName: '', // 现有子仓名称
  subWarehouseName: '', // 规范子仓名称
};

const defaultState = {
  formRef: {}, // 绑定 form 的引用, 可以调用某些 form 的方法  validate(校验表单) 具体可以查看 https://shineout.biz.sheincorp.cn/1.6.x/cn/components/Form
  // 搜索区域+下拉数据
  loading: 1, // 0 loading, 1 load success, 2 load fail  用于搜索和导出
  // 列表区域+分页区域
  pageInfo: {
    pageNum: 1,
    pageSize: getSize(),
    count: 0, // 表格总条数
    pageSizeList: [20, 50, 100], // 表格页显示条数
  },
  list: [],
  addModalVisible: false, // 登记弹框
  addModalData: { ...defaultAddModalData }, // 登记弹框数据
  subWarehouseList: [], // 子仓下拉
  isEditMode: false, // 是否为编辑模式
  editingId: null, // 正在编辑的记录ID
};

export default {
  state: defaultState,
  // 改state属性值
  changeData(state, data) {
    Object.assign(state, data);
  },
  changeaddModalData(state, data) {
    Object.assign(state.addModalData, data);
  },

  // 页面初始化
  * init() {
    yield this.search();
    // 获取全部子仓数据
    const { code, info, msg } = yield queryAllSubWarehouseDataAPI();
    if (code === '0') {
      yield this.changeData({
        subWarehouseList: info,
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 搜索
   */
  * search() {
    const { pageInfo } = yield '';
    const param = {
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    };
    markStatus('loading');
    const { code, info, msg } = yield getListAPI(param);
    if (code === '0') {
      yield this.changeData({
        list: info.data,
        pageInfo: {
          ...pageInfo,
          count: info.meta.count,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },

  /**
   * 分页条改变
   * @param {object} data 分页数组主要有pageNum pageSize
   * @returns
   */
  * handlePaginationChange(data = {}) {
    // 校验通过后执行
    const { pageInfo } = yield '';
    yield this.changeData({
      pageInfo: {
        ...pageInfo,
        ...data,
      },
    });
    yield this.search();
  },

  // 保存新增/编辑
  * handleregister() {
    const { addModalData, isEditMode, editingId } = yield '';
    const params = {
      ...addModalData,
    };

    // 如果是编辑模式，添加ID参数
    if (isEditMode && editingId) {
      params.id = editingId;
    }

    const api = isEditMode ? registerEditAPI : registerAPI;
    const { code, msg } = yield api(clearEmpty(paramTrim(params), [0, '0', false]));
    if (code === '0') {
      Message.success(t(isEditMode ? '编辑成功' : '保存成功'));
      yield this.handleCloseRegisterModal();
      yield this.handlePaginationChange({ pageNum: 1 });
    } else {
      Modal.error({ title: msg });
    }
  },

  // 关闭登记弹框
  * handleCloseRegisterModal() {
    const { formRef } = yield '';
    yield this.changeData({
      addModalVisible: false,
      addModalData: { ...defaultAddModalData },
      isEditMode: false,
      editingId: null,
    });
    if (formRef && formRef.clearValidate) formRef.clearValidate();
  },

  // 处理编辑操作
  * handleEdit(record) {
    // 获取详情数据
    const { code, info, msg } = yield getDetailAPI({ id: record.id });
    if (code === '0') {
      const { subWarehouseList } = yield '';

      // 根据 subWarehouseName 找到对应的子仓项，获取其 nameZh 值
      // 因为 Select 组件使用 format="nameZh"，所以表单值应该是 nameZh
      let selectedSubWarehouseName = info.subWarehouseName || '';
      const matchedSubWarehouse = subWarehouseList.find((item) => item.nameZh === info.subWarehouseName);
      if (matchedSubWarehouse) {
        selectedSubWarehouseName = matchedSubWarehouse.nameZh;
      }

      // 设置编辑模式和数据
      yield this.changeData({
        isEditMode: true,
        editingId: record.id,
        addModalVisible: true,
        addModalData: {
          currentParkName: info.currentParkName || '',
          currentRegionName: info.currentRegionName || '',
          currentSubWarehouseName: info.currentSubWarehouseName || '',
          subWarehouseName: selectedSubWarehouseName,
        },
      });
    } else {
      Modal.error({ title: msg });
    }
  },
};
